import { z } from "zod"

const eventInputs = {
  id: z.number().optional(),
  title: z.string().min(3),
  openness: z.enum([
    "public",
    "publicLimited",
    "community",
    "seniority",
    "private",
    "closed",
  ]),
  location: z.union([z.literal(""), z.string().min(3).max(256).optional()]),
  approval: z.boolean(),
  description: z.union([z.literal(""), z.string().min(10).optional()]),
  starts: z.string().datetime({ offset: true }),
  ends: z.string().datetime({ offset: true }).optional(),
  maxCapacity: z.number().optional(),
  reserveCapacity: z.number().optional(),
  state: z.enum(["open", "ongoing", "ended", "hidden", "cancelled"]),
  share: z.boolean(),
  hasAgeLimit: z.boolean(),
  minCapacity: z.number().optional(),
  smallDescription: z.union([z.literal(""), z.string().min(10).optional()]),
  lat: z.number().optional(),
  lng: z.number().optional(),
}

export const createEventInputs = z.object({
  ...eventInputs,
  openness: z.enum(["public", "publicLimited", "community", "seniority"]),
})

export const createPrivateEventInputs = z.object({
  ...eventInputs,
  openness: z.enum(["public", "private", "closed"]),
})

export type CreateEventInputs = z.infer<typeof createEventInputs>
export type CreatePrivateEventInputs = z.infer<typeof createPrivateEventInputs>
