import { DevTool } from "@hookform/devtools"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { LoadingButton } from "@mui/lab"
import { <PERSON>, Button } from "@mui/material"
import dayjs from "dayjs"
import { useState } from "react"
import { Form<PERSON>rovider, type SubmitHandler, useForm } from "react-hook-form"
import { z } from "zod"

import {
  DEFAULT_LAT,
  DEFAULT_LNG,
  DEFAULT_LOCATION,
} from "../../../config/maps"
import {
  type CreateEventInputs,
  CreatePrivateEventInputs,
  createEventInputs,
  createPrivateEventInputs,
} from "../../../schemas"
import { useModalStore } from "../../../store/useModalStore"
import { trpc } from "../../../trpc/trpc"
import { localToPost } from "../../../utils/transformTime"
import {
  LoaderDialog,
  type LoaderDialogState,
} from "../../common/LoaderDialog/LoaderDialog"
import { FormDateTime } from "../../elements/HookElements/FormDateTime"
import { FormInput } from "../../elements/HookElements/FormInput"
import { FormSelect } from "../../elements/HookElements/FormSelect"
import { FormSwitch } from "../../elements/HookElements/FormSwitch"
import { RTEdit } from "../../elements/HookElements/RTEdit/RTEdit"
import { MAP_MODAL_NAME, MapModal } from "../../modals/MapModal/MapModal"

export interface CreateInfoProps {
  onSuccess: (eventId: number) => void
  edit?: CreateEventInputs & CreatePrivateEventInputs
  communityId?: number
}

export const EventConfiguration = ({
  onSuccess,
  edit,
  communityId,
}: CreateInfoProps) => {
  const [savingData, setSavingData] = useState<LoaderDialogState>(null)
  const { openModal } = useModalStore()
  const methods = useForm<CreateEventInputs & CreatePrivateEventInputs>({
    resolver: zodResolver(
      z.union([createEventInputs, createPrivateEventInputs]),
    ),
    defaultValues: {
      title: edit?.title ?? "",
      openness: edit?.openness ?? "public",
      approval: edit?.approval ?? true,
      location: edit?.location ?? DEFAULT_LOCATION,
      description: edit?.description ?? "",
      starts: edit?.starts ?? localToPost(dayjs().add(1, "day").toDate()),
      ends: edit?.ends ?? undefined,
      state: edit?.state ?? "hidden",
      maxCapacity: edit?.maxCapacity ?? 0,
      reserveCapacity: edit?.reserveCapacity ?? 0,
      share: edit?.share ?? false,
      hasAgeLimit: edit?.hasAgeLimit ?? false,
      minCapacity: edit?.minCapacity ?? 0,
      smallDescription: edit?.smallDescription ?? "",
      lat: edit?.lat ?? DEFAULT_LAT,
      lng: edit?.lng ?? DEFAULT_LNG,
    },
  })

  //

  const onSubmit: SubmitHandler<CreateEventInputs> = async (data) => {
    setSavingData("loading")

    const mutation = communityId
      ? trpc.createEvent.mutate({
          ...(data as CreateEventInputs),
          communityId,
          id: edit ? (edit.id ?? 0) : undefined,
        })
      : trpc.createPrivateEvent.mutate({
          ...(data as CreatePrivateEventInputs),
          id: edit ? (edit.id ?? 0) : undefined,
        })

    mutation
      .then((response) => {
        if (response.success) {
          onSuccess(response.eventId)
        }

        setSavingData(response.success ? null : "failed")
      })
      .catch(() => {
        setSavingData("failed")
      })
  }

  const onError = (error: unknown) => {
    console.error(error)
  }

  const openness = [{ title: "Public", value: "public" }]

  if (!communityId) {
    openness.push({ title: "Private", value: "private" })
    openness.push({ title: "Closed", value: "closed" })
  }

  if (communityId) {
    // openness.push({ title: "Seniority", value: "seniority" })
    openness.push({ title: "Community", value: "community" })
    openness.push({ title: "Public Limited", value: "publicLimited" })
  }

  const publicHelper = "* Public - Anyone can see, anyone can join;"

  const privateHelper =
    "* Private - Only participants and invited can see, only invited can join;<br /> * Closed - Only participants can se, nobody can join;"

  const communityHelper =
    "* Community - Only Members can see, join only members; <br /> * Public Limited - Anyone can see, only members can join;"

  const helper = `${publicHelper} <br /> ${communityId ? communityHelper : privateHelper}`

  return (
    <Box padding={4}>
      <DevTool control={methods.control} placement="top-right" />
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit, onError)}>
          <Box display="flex" flexDirection="column" gap={2}>
            <Box width="100%" maxWidth={350}>
              <FormInput
                label="Title"
                name="title"
                required={true}
                placeholder="Out new event"
              />
            </Box>
            <Box width="100%" maxWidth={350}>
              <FormSelect
                label="How public is your event (availability)?"
                name="openness"
                helper={helper}
                items={openness}
              />
            </Box>
            <Box width="100%" maxWidth={350}>
              <FormSelect
                label="Current event state"
                name="state"
                items={[
                  { title: "Open", value: "open" },
                  { title: "Ongoing", value: "ongoing" },
                  { title: "Ended", value: "ended" },
                  { title: "Hidden", value: "hidden" },
                  { title: "Cancelled", value: "cancelled" },
                ]}
              />
            </Box>
            <Box width="100%" maxWidth={350}>
              <FormInput
                label="Physical location"
                name="location"
                helper="Can add if You community is location based."
                placeholder="Latvia, Riga"
              />
              <Button
                onClick={() => {
                  openModal(MAP_MODAL_NAME)
                }}
              >
                Open map
              </Button>
              <MapModal
                event={{
                  location: methods.getValues("location"),
                  lat: methods.getValues("lat"),
                  lng: methods.getValues("lng"),
                }}
                onChange={(location, lat, lng) => {
                  console.log(location, lat, lng)
                  methods.setValue("location", location ?? DEFAULT_LOCATION)
                  methods.setValue("lat", lat ?? undefined)
                  methods.setValue("lng", lng ?? undefined)
                }}
              />
            </Box>
            <Box width="100%" maxWidth={350}>
              <FormSwitch
                label="Allow users to share games"
                name="allowShare"
                helper="If this is disabled - nobody will be able to share their
                        collection. You will need to go and do it manually for
                        each participant."
              />
            </Box>
            <Box width="100%" maxWidth={350}>
              <FormSwitch
                label="Age limit"
                name="hasAgeLimit"
                helper="If this is enabled - participants will be informed that this is 18+ event."
              />
            </Box>
            <Box width="100%" maxWidth={350}>
              <FormInput
                label="Min capacity"
                name="minCapacity"
                type="number"
                helper="Minimum number of participants for the event to happen. 0 - not limited"
              />
            </Box>
            <Box width="100%" maxWidth={350}>
              <FormInput
                label="Max capacity"
                name="maxCapacity"
                type="number"
                helper="How many people can join. 0 - not limited"
              />
            </Box>
            <Box width="100%" maxWidth={350}>
              <FormInput
                label="Reserve capacity"
                name="reserveCapacity"
                type="number"
                helper="People that can be 'in reserve'. They will see that they are in reserve and receive notification if they get moved to participants if someone drops out."
              />
            </Box>
            <Box width="100%" maxWidth={350}>
              <FormSwitch
                label="Manual participant approval"
                name="approval"
                helper="You will need to approve new partiipants manually if this is
                        enabled. Otherwise anyone who is eligible can join (eg -
                        for public anyone, for others - anyone who has invite)."
              />
            </Box>
            <Box width="100%" maxWidth={350}>
              <FormDateTime label="When does event starts?" name="starts" />
            </Box>
            <Box width="100%" maxWidth={350}>
              <FormDateTime label="When does event ends?" name="ends" />
            </Box>
            <Box width="100%" maxWidth={500}>
              <FormInput
                multiline
                slotProps={{ htmlInput: { maxLength: 200 } }}
                helper="Small description that will be shown on the event card."
                placeholder="Small description"
                label="Small Description"
                name="smallDescription"
                required={false}
              />
            </Box>
            <Box width="100%" maxWidth={700}>
              <RTEdit label="Descrition" name="description" required={false} />
            </Box>
            <Box>
              <LoadingButton
                variant="contained"
                color="primary"
                type="submit"
                loading={savingData === "loading"}
              >
                Save
              </LoadingButton>
            </Box>
          </Box>
        </form>
      </FormProvider>
      <LoaderDialog
        state={savingData}
        title="Upload Community Logo"
        onClose={() => {
          setSavingData(null)
        }}
      />
    </Box>
  )
}
