import { Box, Typography } from "@mui/material"

import { hasPermission } from "../../../../../../common/src/permissions/hasPermissions"
import { useUserStore } from "../../../store/useUserStore"
import { ICEvent } from "../../../types/tRPC.types"

import * as styles from "./eventStatus.module.css"

type EventStatusProps = {
  event: ICEvent
}

export const EventStatus = ({ event }: EventStatusProps) => {
  const user = useUserStore((state) => state.userData)
  if (!event) {
    return null
  }

  const isParticipant = hasPermission(user, "event", "isParticipant", event)
  const isWaiting = hasPermission(user, "event", "isWaiting", event)
  const isRequested = hasPermission(user, "event", "isRequested", event)
  const isReserved = hasPermission(user, "event", "isReserved", event)
  const isHost = hasPermission(user, "event", "isHost", event)

  let status: string | null

  if (isHost) {
    status = "Host"
  } else if (isRequested) {
    status = "Requested"
  } else if (isReserved) {
    status = "Reserved"
  } else if (isWaiting) {
    status = "Interested"
  } else if (isParticipant) {
    status = "Participant"
  } else {
    status = null
  }

  return (
    <Box className={styles.role}>
      <Typography
        variant="h6"
        color={isHost ? "error" : "info"}
        fontWeight={600}
      >
        {status}
      </Typography>
    </Box>
  )
}
