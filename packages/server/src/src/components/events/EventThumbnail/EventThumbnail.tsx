import AccessTimeIcon from "@mui/icons-material/AccessTime"
import {
  Alert,
  Box,
  Button,
  ButtonGroup,
  Card,
  CardActions,
  CardContent,
  CardMedia,
  Typography,
} from "@mui/material"
import { useCallback } from "react"

import { hasPermission } from "../../../../../../common/src/permissions/hasPermissions"
import { createImageLink } from "../../../config/images"
import { useEvent } from "../../../hooks/useEvent"
import { EVENT_ROUTE_INFO } from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import { ICEvent } from "../../../types/tRPC.types"
import { localToLocalWithDay } from "../../../utils/transformTime"
import { LabelTooltip } from "../../elements/LabelTooltip/LabelTooltip"
import { PartyLink } from "../../elements/link/PartyLink/PartyLink"
import { EventStatus } from "../EventStatus/EventStatus"

import { HostInfo } from "./components/HostInfo"
import { MapInfo } from "./components/MapInfo"
import { StateLine } from "./components/StateLine"
import * as styles from "./eventThumbnail.module.css"

export interface ICEventHost {
  id: number
  name: string
  image: string | null
}

interface EventThumbnailProps {
  event: ICEvent
}
export const EventThumbnail = ({ event }: EventThumbnailProps) => {
  const { onChangeUserState } = useEvent()

  const user = useUserStore((state) => state.userData)

  const isParticipant = hasPermission(user, "event", "isParticipant", event)
  const isWaiting = hasPermission(user, "event", "isWaiting", event)
  const isRequested = hasPermission(user, "event", "isRequested", event)
  const isReserved = hasPermission(user, "event", "isReserved", event)
  const canJoin = hasPermission(user, "event", "join", event)

  const handleComplete = useCallback(
    () => (status: string | null) => {
      console.log("Complete", status)
    },
    [event.id],
  )

  const onChangeStatus = useCallback(
    (
      status:
        | "participant"
        | "interested"
        | "requested"
        | "reserved"
        | "notgoing",
    ) => {
      onChangeUserState({ event, status, onComplete: handleComplete })
    },
    [event.id],
  )

  const stillHasReserve =
    (event?.maxCapacity ?? 0) > 0 &&
    (event?.going ?? 0) >= (event?.maxCapacity ?? 0) &&
    (event?.reserveCapacity ?? 0) > 0 &&
    (event?.reserve ?? 0) < (event?.reserveCapacity ?? 0)

  return (
    <Card className={styles.card}>
      <CardMedia
        component="img"
        className={styles.image}
        src={createImageLink("event", "large", event.id, event.image)}
      />
      <CardContent>
        <Box display="flex" flexDirection="column" gap={2}>
          <Box display="flex" gap={1} alignItems="center" flexWrap="nowrap">
            <AccessTimeIcon fontSize="medium" />
            <Typography variant="h6">
              {localToLocalWithDay(event.starts, true)}
            </Typography>
          </Box>
          {event.ends && (
            <Box display="flex" gap={1} alignItems="center" flexWrap="nowrap">
              Till <AccessTimeIcon fontSize="small" />
              <Typography variant="subtitle1">
                {localToLocalWithDay(event.ends, true)}
              </Typography>
            </Box>
          )}
          <MapInfo event={event} />
          <Box>
            <Typography variant="h2" fontSize="1.2rem">
              {event.title}
            </Typography>
          </Box>
          {event.smallDescription && (
            <Box>
              <Typography variant="body1">{event.smallDescription}</Typography>
            </Box>
          )}
          <HostInfo event={event} />
        </Box>
      </CardContent>
      <CardActions>
        <Box display="flex" flexDirection="column" gap={1} width="100%">
          <StateLine event={event} />
          <Box className={styles.bottom}>
            <ButtonGroup>
              {!isParticipant && canJoin && stillHasReserve && (
                <>
                  <Alert>Only reserve spots left!</Alert>
                  <Button
                    size="small"
                    onClick={() => onChangeStatus("reserved")}
                  >
                    Reserve
                  </Button>
                  <LabelTooltip>
                    <Typography variant="body1">
                      Reserve - You will be added to reserve list. If someone
                      drops out you will be moved to participant list.
                    </Typography>
                  </LabelTooltip>
                </>
              )}
              {!isParticipant && canJoin && !isRequested && !isReserved && (
                <Button
                  size="small"
                  onClick={() =>
                    onChangeStatus(
                      event.memberApproval ? "requested" : "participant",
                    )
                  }
                >
                  {event.memberApproval ? "Request" : "Going"}
                </Button>
              )}
              {!isParticipant && !isWaiting && canJoin && (
                <Button
                  size="small"
                  onClick={() => onChangeStatus("interested")}
                >
                  Interested
                </Button>
              )}
              <PartyLink
                variant="contained"
                to={EVENT_ROUTE_INFO}
                params={{ eventId: String(event.id) }}
              >
                Open Event
              </PartyLink>
            </ButtonGroup>
            <EventStatus event={event} />
          </Box>
        </Box>
      </CardActions>
    </Card>
  )
}
