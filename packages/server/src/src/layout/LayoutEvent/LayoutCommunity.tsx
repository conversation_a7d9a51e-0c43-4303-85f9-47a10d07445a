import { Box } from "@mui/material"
import React from "react"

import { FillData } from "../../components/common/FillData/FillData"
import { Modals } from "../Modals"
import * as styles from "../layout.module.css"

export const LayoutCommunity = ({
  children,
}: {
  children: React.ReactNode
}) => {
  return (
    <Box className={styles.container} id="main">
      <FillData />
      <div className={styles.layout}>{children}</div>
      <Modals />
    </Box>
  )
}
