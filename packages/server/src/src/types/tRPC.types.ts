import { ICEventHost } from "../components/events/EventThumbnail/EventThumbnail"
import { type ArrayElement, type RouterOutput } from "../trpc/trpc"

// CommunityExpanded

export type IGamesAll = RouterOutput["communityGamesList2"]

export type ICommunityGame = ArrayElement<IGamesAll["games"]>

export type ITags = IGamesAll["tags"]

export type ITag = ArrayElement<ITags>

export type ITagCategories =
  RouterOutput["communityGamesList2"]["tagCategories"]
export type ITagCategory = ArrayElement<ITagCategories>

export type IGameUsers = RouterOutput["communityGamesList2"]["users"]
export type IGameUser = ArrayElement<IGameUsers>

export type IGameGameUsers =
  RouterOutput["communityGameExtended2"]["game"]["users"]
export type IGameGameUser = ArrayElement<IGameGameUsers>

export type IGameGame = RouterOutput["communityGameExtended2"]["game"]

export type IGameViewTags = RouterOutput["communityGameExtended2"]["tags"]
export type IGameViewTag = ArrayElement<IGameViewTags>

export type IGameViewExpansions =
  RouterOutput["communityGameExtended2"]["expansions"]

export type IGameViewExpansion = ArrayElement<IGameViewExpansions>

export type IUserProfileGames = RouterOutput["getMyInfo"]["games"]

export type IUserProfileGame = ArrayElement<IUserProfileGames>

export type IBasicCommunity = RouterOutput["communityBasic"]

export type IUserProfileGameDetails = RouterOutput["getMyGameInfo"]["game"]

export type IUserProfileGameExpansions =
  RouterOutput["getMyGameInfo"]["expansions"]

export type IUserProfileGameData = RouterOutput["getMyGameInfo"]["myData"]

export type IUserProfileGameExpansion = ArrayElement<IUserProfileGameExpansions>

export type IItemInfo = RouterOutput["getItemInfo"]

export type ICommunityBaseData = RouterOutput["communityBasic"]
export type IEventBaseData = RouterOutput["eventBasic"]

export interface ICEvent {
  id: number
  title: string
  starts: string
  ends?: string | null
  location?: string | null
  maxCapacity?: number | null
  state: string
  role?: string | null
  openness: string
  hosts: ICEventHost[]
  communities: ICEventHost[]
  reserveCapacity?: number | null
  going?: number | null
  reserve?: number | null
  smallDescription?: string | null
  hasAgeLimit?: boolean | null
  minCapacity?: number | null
  hostId?: number | null
  hostName?: string | null
  hostAvatar?: string | null
  hostColor?: string | null
  image?: string | null
  memberApproval?: boolean | null
  lat?: number | null
  lng?: number | null
}
