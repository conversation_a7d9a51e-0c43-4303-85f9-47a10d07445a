import { Box } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import { useCallback } from "react"

import { hasPermission } from "../../../../../../common/src/permissions/hasPermissions"
import { TitleRow } from "../../../components/common/TitleRow/TitleRow"
import { PartyLink } from "../../../components/elements/link/PartyLink/PartyLink"
import { EventFilter } from "../../../components/events/EventFilter/EventFilter"
import { EventSearch } from "../../../components/events/EventSearch/EventSearch"
import { EventsList } from "../../../components/events/EventsList/EventsList"
import { communityEventsRoute } from "../../../routes/community/events.route"
import {
  COMMUNITIES_ROOT_ROUTE,
  COMMUNITY_EVENTS_ROUTE,
  COMMUNITY_EVENT_ROUTE,
} from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import {
  isCommunity,
  useParentRouteData,
} from "../../../utils/pages.rootObject"

export const CommunityEventsPage = () => {
  const events = communityEventsRoute.useLoaderData()
  const search = communityEventsRoute.useSearch()
  const base = useParentRouteData(COMMUNITIES_ROOT_ROUTE)
  const navigate = useNavigate({ from: COMMUNITY_EVENTS_ROUTE })
  const userInfo = useUserStore((state) => state.userData)

  const onChange = useCallback(
    (page: number) => {
      navigate({
        search: {
          page,
        },
      })

      window.scrollTo({ top: 0 })
    },
    [navigate],
  )

  const onSearch = useCallback(
    (searchString: string) => {
      navigate({
        search: {
          ...search,
          search: searchString,
        },
      })

      window.scrollTo({ top: 0 })
    },
    [navigate],
  )

  if (!isCommunity(base)) {
    return null
  }

  return (
    <Box>
      <TitleRow title="Events">
        {hasPermission(userInfo, "community", "event", base) && (
          <Box>
            <PartyLink
              to={COMMUNITY_EVENT_ROUTE}
              params={{ communityId: String(base.id) }}
              variant="outlined"
            >
              Create Event
            </PartyLink>
          </Box>
        )}
      </TitleRow>
      <Box
        display="flex"
        flexDirection="row"
        gap={2}
        alignItems="center"
        justifyContent="center"
      >
        <EventFilter from={COMMUNITY_EVENTS_ROUTE} search={search} />
        <EventSearch search={search.search} onSearch={onSearch} />
      </Box>
      {events && (
        <EventsList
          events={events}
          onPageChange={onChange}
          page={search.page}
          search={search.search}
        />
      )}
    </Box>
  )
}
