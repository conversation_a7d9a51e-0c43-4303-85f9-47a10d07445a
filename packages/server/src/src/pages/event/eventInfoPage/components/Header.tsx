import AccessTimeIcon from "@mui/icons-material/AccessTime"
import { Box, Typography } from "@mui/material"

import { hasPermission } from "../../../../../../../common/src/permissions/hasPermissions"
import { EventStatus } from "../../../../components/events/EventStatus/EventStatus"
import { HostInfo } from "../../../../components/events/EventThumbnail/components/HostInfo"
import { MapInfo } from "../../../../components/events/EventThumbnail/components/MapInfo"
import { StateLine } from "../../../../components/events/EventThumbnail/components/StateLine"
import { MapModal } from "../../../../components/modals/MapModal/MapModal"
import { createImageLink } from "../../../../config/images"
import { EVENT_ROOT_ROUTE } from "../../../../routes/paths"
import { useUserStore } from "../../../../store/useUserStore"
import { isEvent, useParentRouteData } from "../../../../utils/pages.rootObject"
import { localToLocalWithDay } from "../../../../utils/transformTime"

import * as styles from "./header.module.css"

export const Header = () => {
  const event = useParentRouteData(EVENT_ROOT_ROUTE)
  const userData = useUserStore((state) => state.userData)

  if (!isEvent(event) || !userData) {
    return null
  }

  const isParticipant = hasPermission(userData, "event", "isParticipant", event)

  return (
    <Box className={styles.container}>
      <Box className={styles.imageContainer}>
        <img
          src={createImageLink("event", "cover", event.id, event.image)}
          alt={event.title}
          className={styles.image}
        />
      </Box>
      <Box className={styles.contentContainer}>
        <Box className={styles.content}>
          <Box className={styles.titleBox}>
            <Box display="flex" flexDirection="column" gap={2}>
              <Box
                display="flex"
                flexDirection="row"
                gap={4}
                rowGap={1}
                flexWrap="wrap"
              >
                <Box
                  display="flex"
                  gap={1}
                  alignItems="center"
                  flexWrap="nowrap"
                >
                  <AccessTimeIcon fontSize="medium" />
                  <Typography variant="h6">
                    {localToLocalWithDay(event.starts, true)}
                  </Typography>
                </Box>
                {event.ends && (
                  <Box
                    display="flex"
                    gap={1}
                    alignItems="center"
                    flexWrap="nowrap"
                  >
                    Till <AccessTimeIcon fontSize="small" />
                    <Typography variant="subtitle1">
                      {localToLocalWithDay(event.ends, true)}
                    </Typography>
                  </Box>
                )}
              </Box>
              <Box
                display="flex"
                flexDirection="row"
                gap={2}
                flexWrap="wrap"
                alignItems="center"
                justifyContent="space-between"
              >
                <Typography fontWeight="500" variant="h1" fontSize="2rem">
                  {event.title}
                </Typography>
                <EventStatus event={event} />
              </Box>
            </Box>
            <Box className={styles.titleWrapper}></Box>
          </Box>
          <MapInfo event={event} />
          <HostInfo event={event} />
          <StateLine event={event} />
        </Box>
      </Box>
      <MapModal event={event} />
    </Box>
  )
}
