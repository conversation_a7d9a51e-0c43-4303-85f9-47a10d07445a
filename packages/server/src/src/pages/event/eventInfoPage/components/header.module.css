@value isTablet from "../../../../css/size.module.css";

.container {
    width: 100%;
    display: grid;
    position: relative;
}

.imageContainer {
    grid-area: 1 / 1;
    z-index: 0;
}

.contentContainer {
    grid-area: 1 / 1;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.content {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: var(--spacing-4);
    display: flex;
    width: 100%;
    flex-direction: column;
    gap: var(--spacing-2);
    padding: var(--spacing-4);
    max-width: 90%;
    margin: var(--spacing-2) auto auto;
    box-sizing: border-box;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.3)
}

.image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    max-height: 300px;
    border-bottom-left-radius: var(--spacing-2);
    border-bottom-right-radius: var(--spacing-2);
}