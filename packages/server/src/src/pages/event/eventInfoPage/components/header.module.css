@value isTablet from "../../../../css/size.module.css";

.container {
    width: 100%;
    display: grid;
    position: relative;
}

.contentContainer {
    grid-area: 1 / 1;
    align-items: center;
    justify-content: center;
    display: flex;
    z-index: 1; /* Ensure it's above background */
}

.content {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: var(--spacing-4);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
    padding: var(--spacing-4);
    max-width: 90%;
    margin: var(--spacing-2) auto auto;
    box-sizing: border-box;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.3)
}

.image{
    grid-area: 1 / 1;
    z-index: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    max-height: 300px;
    border-bottom-left-radius: var(--spacing-2);
    border-bottom-right-radius: var(--spacing-2);
}