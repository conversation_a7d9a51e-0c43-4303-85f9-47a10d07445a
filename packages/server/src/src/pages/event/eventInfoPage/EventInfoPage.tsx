import { Box, Typography } from "@mui/material"

import { eventInfoRoute } from "../../../routes/event/eventInfo.route"
import { EVENT_ROOT_ROUTE } from "../../../routes/paths"
import { isEvent, useParentRouteData } from "../../../utils/pages.rootObject"
import * as styles from "../../community/communityPage/community.module.css"

import { Header } from "./components/Header"
import { Welcome } from "./components/Welcome"
// import { NewGamesView } from "../../community/communityPage/components/NewGamesView"
// import { NewMembersView } from "../../community/communityPage/components/NewMembersView"
// import { Welcome } from "../../community/communityPage/components/Welcome"

export const EventInfoPage = () => {
  const event = useParentRouteData(EVENT_ROOT_ROUTE)
  const { games, participants } = eventInfoRoute.useLoaderData() ?? {}

  if (!isEvent(event) || !games || !participants) {
    return null
  }

  const isWelcome = (event.description?.length ?? 0) > 7

  return (
    <>
      <Header />
      {isWelcome && (
        <Box className={styles.welcome}>
          <Welcome />
        </Box>
      )}
    </>
  )
}
